import { createClient } from '@supabase/supabase-js'
import { createClientComponentClient, createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

// Use default values if environment variables are not set
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'demo-key'

// Client-side Supabase client with error handling
export const supabase = (() => {
  try {
    return createClient(supabaseUrl, supabaseAnonKey)
  } catch (error) {
    console.warn('Supabase client creation failed:', error)
    return null
  }
})()

// Client component Supabase client with error handling
export const createSupabaseClient = () => {
  try {
    return createClientComponentClient()
  } catch (error) {
    console.warn('Supabase client component creation failed:', error)
    return null
  }
}

// Server component Supabase client with error handling
export const createSupabaseServerClient = () => {
  try {
    return createServerComponentClient({ cookies })
  } catch (error) {
    console.warn('Supabase server client creation failed:', error)
    return null
  }
}

// Database types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          role: 'admin' | 'student'
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          role?: 'admin' | 'student'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          role?: 'admin' | 'student'
          created_at?: string
          updated_at?: string
        }
      }
      categories: {
        Row: {
          id: string
          name: string
          description: string | null
          created_at: string
          created_by: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          created_at?: string
          created_by: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          created_at?: string
          created_by?: string
        }
      }
      files: {
        Row: {
          id: string
          name: string
          description: string | null
          file_path: string
          file_size: number
          file_type: string
          category_id: string | null
          assigned_to: string | null
          uploaded_by: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          file_path: string
          file_size: number
          file_type: string
          category_id?: string | null
          assigned_to?: string | null
          uploaded_by: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          file_path?: string
          file_size?: number
          file_type?: string
          category_id?: string | null
          assigned_to?: string | null
          uploaded_by?: string
          created_at?: string
          updated_at?: string
        }
      }
      file_downloads: {
        Row: {
          id: string
          file_id: string
          user_id: string
          downloaded_at: string
        }
        Insert: {
          id?: string
          file_id: string
          user_id: string
          downloaded_at?: string
        }
        Update: {
          id?: string
          file_id?: string
          user_id?: string
          downloaded_at?: string
        }
      }
    }
  }
}
