# 🗂️ نظام إدارة الملفات | File Management System

نظام شامل لإدارة وتنظيم الملفات مع إمكانيات متقدمة للمشرفين والمستخدمين، مع دعم كامل للغة العربية والإنجليزية.

## ✨ المميزات الرئيسية

### 👑 للمشرف (Admin):
- ✅ **إدارة المستخدمين**: إضافة، تعديل، حذف، تفعيل/تعطيل المستخدمين
- ✅ **مشاهدة جميع الملفات**: الوصول الكامل لجميع ملفات النظام
- ✅ **تنظيم الملفات**: إنشاء وإدارة المجلدات والتصنيفات
- ✅ **التحكم في المساحة**: تحديد المساحة المسموحة لكل مستخدم
- ✅ **التحليلات والإحصائيات**: عدد الملفات، التنزيلات، المساحة المستهلكة
- ✅ **إدارة الصلاحيات**: تحكم كامل في صلاحيات المستخدمين

### 👤 للمستخدم (User):
- ✅ **رفع الملفات**: رفع وتنظيم الملفات في مجلدات مخصصة
- ✅ **تصفح الملفات**: الوصول للملفات الشخصية فقط
- ✅ **تصنيف الملفات**: تنظيم الملفات (دراسية، شخصية، صور، تقارير...)
- ✅ **إدارة الملفات**: تنزيل، حذف، تعديل الملفات الشخصية
- ✅ **مشاركة الملفات**: إنشاء روابط مشاركة آمنة
- ✅ **البحث الذكي**: البحث حسب الاسم، النوع، الحجم، التاريخ

## 🔧 الخصائص التقنية

| الخاصية | الوصف |
|---------|--------|
| 🔍 **بحث ذكي** | فلترة متقدمة حسب الاسم، النوع، الحجم، التاريخ |
| 📂 **مجلدات مخصصة** | إنشاء مجلدات هرمية لتنظيم الملفات |
| 📑 **أنواع ملفات مدعومة** | PDF, Word, Excel, صور، فيديو، صوت، وأكثر |
| 📤 **رفع بالسحب والإفلات** | تجربة مستخدم سلسة وسهلة |
| 🧠 **نظام الوسوم** | إضافة وسوم للملفات لتسهيل البحث |
| 🔒 **صلاحيات متقدمة** | ملفات خاصة، عامة، أو مشتركة برابط |
| 📊 **لوحة تحكم شاملة** | إحصائيات مفصلة وإدارة متقدمة |
| 🌙 **وضع ليلي/نهاري** | تجربة مريحة للعينين |
| 🌐 **دعم متعدد اللغات** | عربي وإنجليزي مع RTL |

## 🚀 التقنيات المستخدمة

- **Frontend**: Next.js 14 + React + TypeScript
- **Styling**: Tailwind CSS مع دعم RTL
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **File Storage**: Supabase Storage
- **UI Components**: Lucide React Icons
- **Animations**: Framer Motion
- **State Management**: React Context

## 📦 التثبيت والإعداد

### 1. متطلبات النظام
- Node.js 18+ 
- npm أو yarn
- حساب Supabase

### 2. تحميل المشروع
```bash
git clone <repository-url>
cd file-management-system
npm install
```

### 3. إعداد متغيرات البيئة
انسخ ملف `.env.example` إلى `.env.local` وأضف بيانات Supabase:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# App Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_random_secret

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif,mp4,mp3,wav
```

### 4. إعداد قاعدة البيانات
1. اذهب إلى [Supabase Dashboard](https://supabase.com/dashboard)
2. اختر مشروعك
3. اذهب إلى SQL Editor
4. انسخ والصق محتوى ملف `supabase-setup.sql`
5. انقر على "Run" لتنفيذ الكود

### 5. تشغيل المشروع
```bash
npm run dev
```

سيعمل المشروع على `http://localhost:3000`

## 🗄️ هيكل قاعدة البيانات

### الجداول الرئيسية:
- **profiles**: بيانات المستخدمين والأدوار
- **folders**: المجلدات الهرمية
- **files**: معلومات الملفات المرفوعة
- **file_shares**: مشاركة الملفات
- **downloads**: سجل تحميل الملفات

### الأمان:
- **Row Level Security (RLS)** مفعل على جميع الجداول
- **سياسات أمان متقدمة** لحماية البيانات
- **تشفير الملفات** في التخزين السحابي

## 🎯 كيفية الاستخدام

### للمشرف:
1. سجل الدخول بحساب المشرف
2. استخدم لوحة التحكم لإدارة المستخدمين
3. راقب الإحصائيات والتحليلات
4. أدر المساحة التخزينية للمستخدمين

### للمستخدم:
1. سجل الدخول بحسابك
2. أنشئ مجلدات لتنظيم ملفاتك
3. ارفع الملفات بالسحب والإفلات
4. استخدم البحث للعثور على ملفات محددة
5. شارك الملفات مع الآخرين

## 🔐 الأمان والخصوصية

- **مصادقة آمنة** مع Supabase Auth
- **تشفير البيانات** في النقل والتخزين
- **صلاحيات محددة** لكل نوع مستخدم
- **حماية من الهجمات** الشائعة
- **نسخ احتياطية** تلقائية

## 🌟 المميزات المتقدمة

- **تصميم متجاوب** لجميع الأجهزة
- **دعم كامل للعربية** مع اتجاه RTL
- **وضع ليلي/نهاري** تلقائي
- **بحث فوري** مع نتائج سريعة
- **معاينة الملفات** المدمجة
- **إشعارات تفاعلية** للعمليات

## 📱 التوافق

- ✅ **المتصفحات**: Chrome, Firefox, Safari, Edge
- ✅ **الأجهزة**: Desktop, Tablet, Mobile
- ✅ **أنظمة التشغيل**: Windows, macOS, Linux, iOS, Android

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

- 📧 **البريد الإلكتروني**: <EMAIL>
- 💬 **Discord**: [رابط الخادم]
- 📖 **الوثائق**: [رابط الوثائق]
- 🐛 **تقرير الأخطاء**: [GitHub Issues]

---

**ملاحظة**: تأكد من إعداد Supabase بشكل صحيح قبل تشغيل المشروع!
