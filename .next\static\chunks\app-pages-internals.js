/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app-pages-internals"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=false!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=false! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/hooks-server-context.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/hooks-server-context.js ***!
  \**************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DYNAMIC_ERROR_CODE: function() {\n        return DYNAMIC_ERROR_CODE;\n    },\n    DynamicServerError: function() {\n        return DynamicServerError;\n    }\n});\nconst DYNAMIC_ERROR_CODE = \"DYNAMIC_SERVER_USAGE\";\nclass DynamicServerError extends Error {\n    constructor(type){\n        super(\"Dynamic server usage: \" + type);\n        this.digest = DYNAMIC_ERROR_CODE;\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hooks-server-context.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/hooks-server-context.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next/dist/client/components/layout-router.js ***!
  \*******************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return OuterLayoutRouter;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _reactdom = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _fetchserverresponse = __webpack_require__(/*! ./router-reducer/fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _infinitepromise = __webpack_require__(/*! ./infinite-promise */ \"(app-pages-browser)/./node_modules/next/dist/client/components/infinite-promise.js\");\nconst _errorboundary = __webpack_require__(/*! ./error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\");\nconst _matchsegments = __webpack_require__(/*! ./match-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/match-segments.js\");\nconst _handlesmoothscroll = __webpack_require__(/*! ../../shared/lib/router/utils/handle-smooth-scroll */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nconst _redirectboundary = __webpack_require__(/*! ./redirect-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js\");\nconst _notfoundboundary = __webpack_require__(/*! ./not-found-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js\");\nconst _getsegmentvalue = __webpack_require__(/*! ./router-reducer/reducers/get-segment-value */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/get-segment-value.js\");\nconst _createroutercachekey = __webpack_require__(/*! ./router-reducer/create-router-cache-key */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-router-cache-key.js\");\nconst _createrecordfromthenable = __webpack_require__(/*! ./router-reducer/create-record-from-thenable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-record-from-thenable.js\");\n/**\n * Add refetch marker to router state at the point of the current layout segment.\n * This ensures the response returned is not further down than the current layout segment.\n */ function walkAddRefetch(segmentPathToWalk, treeToRecreate) {\n    if (segmentPathToWalk) {\n        const [segment, parallelRouteKey] = segmentPathToWalk;\n        const isLast = segmentPathToWalk.length === 2;\n        if ((0, _matchsegments.matchSegment)(treeToRecreate[0], segment)) {\n            if (treeToRecreate[1].hasOwnProperty(parallelRouteKey)) {\n                if (isLast) {\n                    const subTree = walkAddRefetch(undefined, treeToRecreate[1][parallelRouteKey]);\n                    return [\n                        treeToRecreate[0],\n                        {\n                            ...treeToRecreate[1],\n                            [parallelRouteKey]: [\n                                subTree[0],\n                                subTree[1],\n                                subTree[2],\n                                \"refetch\"\n                            ]\n                        }\n                    ];\n                }\n                return [\n                    treeToRecreate[0],\n                    {\n                        ...treeToRecreate[1],\n                        [parallelRouteKey]: walkAddRefetch(segmentPathToWalk.slice(2), treeToRecreate[1][parallelRouteKey])\n                    }\n                ];\n            }\n        }\n    }\n    return treeToRecreate;\n}\n// TODO-APP: Replace with new React API for finding dom nodes without a `ref` when available\n/**\n * Wraps ReactDOM.findDOMNode with additional logic to hide React Strict Mode warning\n */ function findDOMNode(instance) {\n    // Tree-shake for server bundle\n    if (false) {}\n    // Only apply strict mode warning when not in production\n    if (true) {\n        const originalConsoleError = console.error;\n        try {\n            console.error = function() {\n                for(var _len = arguments.length, messages = new Array(_len), _key = 0; _key < _len; _key++){\n                    messages[_key] = arguments[_key];\n                }\n                // Ignore strict mode warning for the findDomNode call below\n                if (!messages[0].includes(\"Warning: %s is deprecated in StrictMode.\")) {\n                    originalConsoleError(...messages);\n                }\n            };\n            return _reactdom.default.findDOMNode(instance);\n        } finally{\n            console.error = originalConsoleError;\n        }\n    }\n    return _reactdom.default.findDOMNode(instance);\n}\nconst rectProperties = [\n    \"bottom\",\n    \"height\",\n    \"left\",\n    \"right\",\n    \"top\",\n    \"width\",\n    \"x\",\n    \"y\"\n];\n/**\n * Check if a HTMLElement is hidden or fixed/sticky position\n */ function shouldSkipElement(element) {\n    // we ignore fixed or sticky positioned elements since they'll likely pass the \"in-viewport\" check\n    // and will result in a situation we bail on scroll because of something like a fixed nav,\n    // even though the actual page content is offscreen\n    if ([\n        \"sticky\",\n        \"fixed\"\n    ].includes(getComputedStyle(element).position)) {\n        if (true) {\n            console.warn(\"Skipping auto-scroll behavior due to `position: sticky` or `position: fixed` on element:\", element);\n        }\n        return true;\n    }\n    // Uses `getBoundingClientRect` to check if the element is hidden instead of `offsetParent`\n    // because `offsetParent` doesn't consider document/body\n    const rect = element.getBoundingClientRect();\n    return rectProperties.every((item)=>rect[item] === 0);\n}\n/**\n * Check if the top corner of the HTMLElement is in the viewport.\n */ function topOfElementInViewport(element, viewportHeight) {\n    const rect = element.getBoundingClientRect();\n    return rect.top >= 0 && rect.top <= viewportHeight;\n}\n/**\n * Find the DOM node for a hash fragment.\n * If `top` the page has to scroll to the top of the page. This mirrors the browser's behavior.\n * If the hash fragment is an id, the page has to scroll to the element with that id.\n * If the hash fragment is a name, the page has to scroll to the first element with that name.\n */ function getHashFragmentDomNode(hashFragment) {\n    // If the hash fragment is `top` the page has to scroll to the top of the page.\n    if (hashFragment === \"top\") {\n        return document.body;\n    }\n    var _document_getElementById;\n    // If the hash fragment is an id, the page has to scroll to the element with that id.\n    return (_document_getElementById = document.getElementById(hashFragment)) != null ? _document_getElementById : document.getElementsByName(hashFragment)[0];\n}\nclass InnerScrollAndFocusHandler extends _react.default.Component {\n    componentDidMount() {\n        this.handlePotentialScroll();\n    }\n    componentDidUpdate() {\n        // Because this property is overwritten in handlePotentialScroll it's fine to always run it when true as it'll be set to false for subsequent renders.\n        if (this.props.focusAndScrollRef.apply) {\n            this.handlePotentialScroll();\n        }\n    }\n    render() {\n        return this.props.children;\n    }\n    constructor(...args){\n        super(...args);\n        this.handlePotentialScroll = ()=>{\n            // Handle scroll and focus, it's only applied once in the first useEffect that triggers that changed.\n            const { focusAndScrollRef, segmentPath } = this.props;\n            if (focusAndScrollRef.apply) {\n                // segmentPaths is an array of segment paths that should be scrolled to\n                // if the current segment path is not in the array, the scroll is not applied\n                // unless the array is empty, in which case the scroll is always applied\n                if (focusAndScrollRef.segmentPaths.length !== 0 && !focusAndScrollRef.segmentPaths.some((scrollRefSegmentPath)=>segmentPath.every((segment, index)=>(0, _matchsegments.matchSegment)(segment, scrollRefSegmentPath[index])))) {\n                    return;\n                }\n                let domNode = null;\n                const hashFragment = focusAndScrollRef.hashFragment;\n                if (hashFragment) {\n                    domNode = getHashFragmentDomNode(hashFragment);\n                }\n                // `findDOMNode` is tricky because it returns just the first child if the component is a fragment.\n                // This already caused a bug where the first child was a <link/> in head.\n                if (!domNode) {\n                    domNode = findDOMNode(this);\n                }\n                // If there is no DOM node this layout-router level is skipped. It'll be handled higher-up in the tree.\n                if (!(domNode instanceof Element)) {\n                    return;\n                }\n                // Verify if the element is a HTMLElement and if we want to consider it for scroll behavior.\n                // If the element is skipped, try to select the next sibling and try again.\n                while(!(domNode instanceof HTMLElement) || shouldSkipElement(domNode)){\n                    // No siblings found that match the criteria are found, so handle scroll higher up in the tree instead.\n                    if (domNode.nextElementSibling === null) {\n                        return;\n                    }\n                    domNode = domNode.nextElementSibling;\n                }\n                // State is mutated to ensure that the focus and scroll is applied only once.\n                focusAndScrollRef.apply = false;\n                focusAndScrollRef.hashFragment = null;\n                focusAndScrollRef.segmentPaths = [];\n                (0, _handlesmoothscroll.handleSmoothScroll)(()=>{\n                    // In case of hash scroll, we only need to scroll the element into view\n                    if (hashFragment) {\n                        domNode.scrollIntoView();\n                        return;\n                    }\n                    // Store the current viewport height because reading `clientHeight` causes a reflow,\n                    // and it won't change during this function.\n                    const htmlElement = document.documentElement;\n                    const viewportHeight = htmlElement.clientHeight;\n                    // If the element's top edge is already in the viewport, exit early.\n                    if (topOfElementInViewport(domNode, viewportHeight)) {\n                        return;\n                    }\n                    // Otherwise, try scrolling go the top of the document to be backward compatible with pages\n                    // scrollIntoView() called on `<html/>` element scrolls horizontally on chrome and firefox (that shouldn't happen)\n                    // We could use it to scroll horizontally following RTL but that also seems to be broken - it will always scroll left\n                    // scrollLeft = 0 also seems to ignore RTL and manually checking for RTL is too much hassle so we will scroll just vertically\n                    htmlElement.scrollTop = 0;\n                    // Scroll to domNode if domNode is not in viewport when scrolled to top of document\n                    if (!topOfElementInViewport(domNode, viewportHeight)) {\n                        domNode.scrollIntoView();\n                    }\n                }, {\n                    // We will force layout by querying domNode position\n                    dontForceLayout: true,\n                    onlyHashChange: focusAndScrollRef.onlyHashChange\n                });\n                // Mutate after scrolling so that it can be read by `handleSmoothScroll`\n                focusAndScrollRef.onlyHashChange = false;\n                // Set focus on the element\n                domNode.focus();\n            }\n        };\n    }\n}\nfunction ScrollAndFocusHandler(param) {\n    let { segmentPath, children } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.GlobalLayoutRouterContext);\n    if (!context) {\n        throw new Error(\"invariant global layout router not mounted\");\n    }\n    return /*#__PURE__*/ _react.default.createElement(InnerScrollAndFocusHandler, {\n        segmentPath: segmentPath,\n        focusAndScrollRef: context.focusAndScrollRef\n    }, children);\n}\n_c = ScrollAndFocusHandler;\n/**\n * InnerLayoutRouter handles rendering the provided segment based on the cache.\n */ function InnerLayoutRouter(param) {\n    let { parallelRouterKey, url, childNodes, childProp, segmentPath, tree, // isActive,\n    cacheKey } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.GlobalLayoutRouterContext);\n    if (!context) {\n        throw new Error(\"invariant global layout router not mounted\");\n    }\n    const { buildId, changeByServerResponse, tree: fullTree } = context;\n    // Read segment path from the parallel router cache node.\n    let childNode = childNodes.get(cacheKey);\n    // If childProp is available this means it's the Flight / SSR case.\n    if (childProp && // TODO-APP: verify if this can be null based on user code\n    childProp.current !== null) {\n        if (!childNode) {\n            // Add the segment's subTreeData to the cache.\n            // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n            childNode = {\n                status: _approutercontextsharedruntime.CacheStates.READY,\n                data: null,\n                subTreeData: childProp.current,\n                parallelRoutes: new Map()\n            };\n            childNodes.set(cacheKey, childNode);\n        } else {\n            if (childNode.status === _approutercontextsharedruntime.CacheStates.LAZY_INITIALIZED) {\n                // @ts-expect-error we're changing it's type!\n                childNode.status = _approutercontextsharedruntime.CacheStates.READY;\n                // @ts-expect-error\n                childNode.subTreeData = childProp.current;\n            }\n        }\n    }\n    // When childNode is not available during rendering client-side we need to fetch it from the server.\n    if (!childNode || childNode.status === _approutercontextsharedruntime.CacheStates.LAZY_INITIALIZED) {\n        /**\n     * Router state with refetch marker added\n     */ // TODO-APP: remove ''\n        const refetchTree = walkAddRefetch([\n            \"\",\n            ...segmentPath\n        ], fullTree);\n        childNode = {\n            status: _approutercontextsharedruntime.CacheStates.DATA_FETCH,\n            data: (0, _createrecordfromthenable.createRecordFromThenable)((0, _fetchserverresponse.fetchServerResponse)(new URL(url, location.origin), refetchTree, context.nextUrl, buildId)),\n            subTreeData: null,\n            head: childNode && childNode.status === _approutercontextsharedruntime.CacheStates.LAZY_INITIALIZED ? childNode.head : undefined,\n            parallelRoutes: childNode && childNode.status === _approutercontextsharedruntime.CacheStates.LAZY_INITIALIZED ? childNode.parallelRoutes : new Map()\n        };\n        /**\n     * Flight data fetch kicked off during render and put into the cache.\n     */ childNodes.set(cacheKey, childNode);\n    }\n    // This case should never happen so it throws an error. It indicates there's a bug in the Next.js.\n    if (!childNode) {\n        throw new Error(\"Child node should always exist\");\n    }\n    // This case should never happen so it throws an error. It indicates there's a bug in the Next.js.\n    if (childNode.subTreeData && childNode.data) {\n        throw new Error(\"Child node should not have both subTreeData and data\");\n    }\n    // If cache node has a data request we have to unwrap response by `use` and update the cache.\n    if (childNode.data) {\n        /**\n     * Flight response data\n     */ // When the data has not resolved yet `use` will suspend here.\n        const [flightData, overrideCanonicalUrl] = (0, _react.use)(childNode.data);\n        // segmentPath from the server does not match the layout's segmentPath\n        childNode.data = null;\n        // setTimeout is used to start a new transition during render, this is an intentional hack around React.\n        setTimeout(()=>{\n            (0, _react.startTransition)(()=>{\n                changeByServerResponse(fullTree, flightData, overrideCanonicalUrl);\n            });\n        });\n        // Suspend infinitely as `changeByServerResponse` will cause a different part of the tree to be rendered.\n        (0, _react.use)((0, _infinitepromise.createInfinitePromise)());\n    }\n    // If cache node has no subTreeData and no data request we have to infinitely suspend as the data will likely flow in from another place.\n    // TODO-APP: double check users can't return null in a component that will kick in here.\n    if (!childNode.subTreeData) {\n        (0, _react.use)((0, _infinitepromise.createInfinitePromise)());\n    }\n    const subtree = /*#__PURE__*/ _react.default.createElement(_approutercontextsharedruntime.LayoutRouterContext.Provider, {\n        value: {\n            tree: tree[1][parallelRouterKey],\n            childNodes: childNode.parallelRoutes,\n            // TODO-APP: overriding of url for parallel routes\n            url: url\n        }\n    }, childNode.subTreeData);\n    // Ensure root layout is not wrapped in a div as the root layout renders `<html>`\n    return subtree;\n}\n_c1 = InnerLayoutRouter;\n/**\n * Renders suspense boundary with the provided \"loading\" property as the fallback.\n * If no loading property is provided it renders the children without a suspense boundary.\n */ function LoadingBoundary(param) {\n    let { children, loading, loadingStyles, loadingScripts, hasLoading } = param;\n    if (hasLoading) {\n        return /*#__PURE__*/ _react.default.createElement(_react.Suspense, {\n            fallback: /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, loadingStyles, loadingScripts, loading)\n        }, children);\n    }\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, children);\n}\n_c2 = LoadingBoundary;\nfunction OuterLayoutRouter(param) {\n    let { parallelRouterKey, segmentPath, childProp, error, errorStyles, errorScripts, templateStyles, templateScripts, loading, loadingStyles, loadingScripts, hasLoading, template, notFound, notFoundStyles, styles } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.LayoutRouterContext);\n    if (!context) {\n        throw new Error(\"invariant expected layout router to be mounted\");\n    }\n    const { childNodes, tree, url } = context;\n    // Get the current parallelRouter cache node\n    let childNodesForParallelRouter = childNodes.get(parallelRouterKey);\n    // If the parallel router cache node does not exist yet, create it.\n    // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n    if (!childNodesForParallelRouter) {\n        childNodesForParallelRouter = new Map();\n        childNodes.set(parallelRouterKey, childNodesForParallelRouter);\n    }\n    // Get the active segment in the tree\n    // The reason arrays are used in the data format is that these are transferred from the server to the browser so it's optimized to save bytes.\n    const treeSegment = tree[1][parallelRouterKey][0];\n    const childPropSegment = childProp.segment;\n    // If segment is an array it's a dynamic route and we want to read the dynamic route value as the segment to get from the cache.\n    const currentChildSegmentValue = (0, _getsegmentvalue.getSegmentValue)(treeSegment);\n    /**\n   * Decides which segments to keep rendering, all segments that are not active will be wrapped in `<Offscreen>`.\n   */ // TODO-APP: Add handling of `<Offscreen>` when it's available.\n    const preservedSegments = [\n        treeSegment\n    ];\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, styles, preservedSegments.map((preservedSegment)=>{\n        const isChildPropSegment = (0, _matchsegments.matchSegment)(preservedSegment, childPropSegment);\n        const preservedSegmentValue = (0, _getsegmentvalue.getSegmentValue)(preservedSegment);\n        const cacheKey = (0, _createroutercachekey.createRouterCacheKey)(preservedSegment);\n        return(/*\n            - Error boundary\n              - Only renders error boundary if error component is provided.\n              - Rendered for each segment to ensure they have their own error state.\n            - Loading boundary\n              - Only renders suspense boundary if loading components is provided.\n              - Rendered for each segment to ensure they have their own loading state.\n              - Passed to the router during rendering to ensure it can be immediately rendered when suspending on a Flight fetch.\n          */ /*#__PURE__*/ _react.default.createElement(_approutercontextsharedruntime.TemplateContext.Provider, {\n            key: (0, _createroutercachekey.createRouterCacheKey)(preservedSegment, true),\n            value: /*#__PURE__*/ _react.default.createElement(ScrollAndFocusHandler, {\n                segmentPath: segmentPath\n            }, /*#__PURE__*/ _react.default.createElement(_errorboundary.ErrorBoundary, {\n                errorComponent: error,\n                errorStyles: errorStyles,\n                errorScripts: errorScripts\n            }, /*#__PURE__*/ _react.default.createElement(LoadingBoundary, {\n                hasLoading: hasLoading,\n                loading: loading,\n                loadingStyles: loadingStyles,\n                loadingScripts: loadingScripts\n            }, /*#__PURE__*/ _react.default.createElement(_notfoundboundary.NotFoundBoundary, {\n                notFound: notFound,\n                notFoundStyles: notFoundStyles\n            }, /*#__PURE__*/ _react.default.createElement(_redirectboundary.RedirectBoundary, null, /*#__PURE__*/ _react.default.createElement(InnerLayoutRouter, {\n                parallelRouterKey: parallelRouterKey,\n                url: url,\n                tree: tree,\n                childNodes: childNodesForParallelRouter,\n                childProp: isChildPropSegment ? childProp : null,\n                segmentPath: segmentPath,\n                cacheKey: cacheKey,\n                isActive: currentChildSegmentValue === preservedSegmentValue\n            }))))))\n        }, templateStyles, templateScripts, template));\n    }));\n}\n_c3 = OuterLayoutRouter;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=layout-router.js.map\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScrollAndFocusHandler\");\n$RefreshReg$(_c1, \"InnerLayoutRouter\");\n$RefreshReg$(_c2, \"LoadingBoundary\");\n$RefreshReg$(_c3, \"OuterLayoutRouter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/maybe-postpone.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/client/components/maybe-postpone.js ***!
  \********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"maybePostpone\", ({\n    enumerable: true,\n    get: function() {\n        return maybePostpone;\n    }\n}));\nfunction maybePostpone(staticGenerationStore, reason) {\n    // If we aren't performing a static generation or we aren't using PPR then\n    // we don't need to postpone.\n    if (!staticGenerationStore.isStaticGeneration || !staticGenerationStore.experimental.ppr) {\n        return;\n    }\n    // App Route's cannot be postponed, so we only postpone if it's a page. If the\n    // postpone API is available, use it now.\n    const React = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n    if (typeof React.unstable_postpone !== \"function\") return;\n    React.unstable_postpone(reason);\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=maybe-postpone.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/maybe-postpone.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/render-from-template-context.js ***!
  \**********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return RenderFromTemplateContext;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nfunction RenderFromTemplateContext() {\n    const children = (0, _react.useContext)(_approutercontextsharedruntime.TemplateContext);\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, children);\n}\n_c = RenderFromTemplateContext;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=render-from-template-context.js.map\nvar _c;\n$RefreshReg$(_c, \"RenderFromTemplateContext\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/searchparams-bailout-proxy.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/searchparams-bailout-proxy.js ***!
  \********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createSearchParamsBailoutProxy\", ({\n    enumerable: true,\n    get: function() {\n        return createSearchParamsBailoutProxy;\n    }\n}));\nconst _staticgenerationbailout = __webpack_require__(/*! ./static-generation-bailout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js\");\nfunction createSearchParamsBailoutProxy() {\n    return new Proxy({}, {\n        get (_target, prop) {\n            // React adds some properties on the object when serializing for client components\n            if (typeof prop === \"string\") {\n                (0, _staticgenerationbailout.staticGenerationBailout)(\"searchParams.\" + prop);\n            }\n        }\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=searchparams-bailout-proxy.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvc2VhcmNocGFyYW1zLWJhaWxvdXQtcHJveHkuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYkEsOENBQTZDO0lBQ3pDRyxPQUFPO0FBQ1gsQ0FBQyxFQUFDO0FBQ0ZILGtFQUFpRTtJQUM3REksWUFBWTtJQUNaQyxLQUFLO1FBQ0QsT0FBT0M7SUFDWDtBQUNKLENBQUMsRUFBQztBQUNGLE1BQU1DLDJCQUEyQkMsbUJBQU9BLENBQUMsZ0lBQTZCO0FBQ3RFLFNBQVNGO0lBQ0wsT0FBTyxJQUFJRyxNQUFNLENBQUMsR0FBRztRQUNqQkosS0FBS0ssT0FBTyxFQUFFQyxJQUFJO1lBQ2Qsa0ZBQWtGO1lBQ2xGLElBQUksT0FBT0EsU0FBUyxVQUFVO2dCQUN6QixJQUFHSix5QkFBeUJLLHVCQUF1QixFQUFFLGtCQUFrQkQ7WUFDNUU7UUFDSjtJQUNKO0FBQ0o7QUFFQSxJQUFJLENBQUMsT0FBT1QsUUFBUVcsT0FBTyxLQUFLLGNBQWUsT0FBT1gsUUFBUVcsT0FBTyxLQUFLLFlBQVlYLFFBQVFXLE9BQU8sS0FBSyxJQUFJLEtBQU0sT0FBT1gsUUFBUVcsT0FBTyxDQUFDQyxVQUFVLEtBQUssYUFBYTtJQUNyS2QsT0FBT0MsY0FBYyxDQUFDQyxRQUFRVyxPQUFPLEVBQUUsY0FBYztRQUFFVixPQUFPO0lBQUs7SUFDbkVILE9BQU9lLE1BQU0sQ0FBQ2IsUUFBUVcsT0FBTyxFQUFFWDtJQUMvQmMsT0FBT2QsT0FBTyxHQUFHQSxRQUFRVyxPQUFPO0FBQ2xDLEVBRUEsc0RBQXNEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvc2VhcmNocGFyYW1zLWJhaWxvdXQtcHJveHkuanM/YjZkZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcImNyZWF0ZVNlYXJjaFBhcmFtc0JhaWxvdXRQcm94eVwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gY3JlYXRlU2VhcmNoUGFyYW1zQmFpbG91dFByb3h5O1xuICAgIH1cbn0pO1xuY29uc3QgX3N0YXRpY2dlbmVyYXRpb25iYWlsb3V0ID0gcmVxdWlyZShcIi4vc3RhdGljLWdlbmVyYXRpb24tYmFpbG91dFwiKTtcbmZ1bmN0aW9uIGNyZWF0ZVNlYXJjaFBhcmFtc0JhaWxvdXRQcm94eSgpIHtcbiAgICByZXR1cm4gbmV3IFByb3h5KHt9LCB7XG4gICAgICAgIGdldCAoX3RhcmdldCwgcHJvcCkge1xuICAgICAgICAgICAgLy8gUmVhY3QgYWRkcyBzb21lIHByb3BlcnRpZXMgb24gdGhlIG9iamVjdCB3aGVuIHNlcmlhbGl6aW5nIGZvciBjbGllbnQgY29tcG9uZW50c1xuICAgICAgICAgICAgaWYgKHR5cGVvZiBwcm9wID09PSBcInN0cmluZ1wiKSB7XG4gICAgICAgICAgICAgICAgKDAsIF9zdGF0aWNnZW5lcmF0aW9uYmFpbG91dC5zdGF0aWNHZW5lcmF0aW9uQmFpbG91dCkoXCJzZWFyY2hQYXJhbXMuXCIgKyBwcm9wKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH0pO1xufVxuXG5pZiAoKHR5cGVvZiBleHBvcnRzLmRlZmF1bHQgPT09ICdmdW5jdGlvbicgfHwgKHR5cGVvZiBleHBvcnRzLmRlZmF1bHQgPT09ICdvYmplY3QnICYmIGV4cG9ydHMuZGVmYXVsdCAhPT0gbnVsbCkpICYmIHR5cGVvZiBleHBvcnRzLmRlZmF1bHQuX19lc01vZHVsZSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMuZGVmYXVsdCwgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuICBPYmplY3QuYXNzaWduKGV4cG9ydHMuZGVmYXVsdCwgZXhwb3J0cyk7XG4gIG1vZHVsZS5leHBvcnRzID0gZXhwb3J0cy5kZWZhdWx0O1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm94eS5qcy5tYXAiXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJlbnVtZXJhYmxlIiwiZ2V0IiwiY3JlYXRlU2VhcmNoUGFyYW1zQmFpbG91dFByb3h5IiwiX3N0YXRpY2dlbmVyYXRpb25iYWlsb3V0IiwicmVxdWlyZSIsIlByb3h5IiwiX3RhcmdldCIsInByb3AiLCJzdGF0aWNHZW5lcmF0aW9uQmFpbG91dCIsImRlZmF1bHQiLCJfX2VzTW9kdWxlIiwiYXNzaWduIiwibW9kdWxlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/searchparams-bailout-proxy.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/client/components/static-generation-async-storage.external.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/static-generation-async-storage.external.js ***!
  \**********************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"staticGenerationAsyncStorage\", ({\n    enumerable: true,\n    get: function() {\n        return staticGenerationAsyncStorage;\n    }\n}));\nconst _asynclocalstorage = __webpack_require__(/*! ./async-local-storage */ \"(shared)/./node_modules/next/dist/client/components/async-local-storage.js\");\nconst staticGenerationAsyncStorage = (0, _asynclocalstorage.createAsyncLocalStorage)();\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=static-generation-async-storage.external.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/client/components/static-generation-async-storage.external.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/static-generation-bailout.js ***!
  \*******************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"staticGenerationBailout\", ({\n    enumerable: true,\n    get: function() {\n        return staticGenerationBailout;\n    }\n}));\nconst _hooksservercontext = __webpack_require__(/*! ./hooks-server-context */ \"(app-pages-browser)/./node_modules/next/dist/client/components/hooks-server-context.js\");\nconst _maybepostpone = __webpack_require__(/*! ./maybe-postpone */ \"(app-pages-browser)/./node_modules/next/dist/client/components/maybe-postpone.js\");\nconst _staticgenerationasyncstorageexternal = __webpack_require__(/*! ./static-generation-async-storage.external */ \"(shared)/./node_modules/next/dist/client/components/static-generation-async-storage.external.js\");\nclass StaticGenBailoutError extends Error {\n    constructor(...args){\n        super(...args);\n        this.code = \"NEXT_STATIC_GEN_BAILOUT\";\n    }\n}\nfunction formatErrorMessage(reason, opts) {\n    const { dynamic, link } = opts || {};\n    const suffix = link ? \" See more info here: \" + link : \"\";\n    return \"Page\" + (dynamic ? ' with `dynamic = \"' + dynamic + '\"`' : \"\") + \" couldn't be rendered statically because it used `\" + reason + \"`.\" + suffix;\n}\nconst staticGenerationBailout = (reason, opts)=>{\n    const staticGenerationStore = _staticgenerationasyncstorageexternal.staticGenerationAsyncStorage.getStore();\n    if (!staticGenerationStore) return false;\n    if (staticGenerationStore.forceStatic) {\n        return true;\n    }\n    if (staticGenerationStore.dynamicShouldError) {\n        var _opts_dynamic;\n        throw new StaticGenBailoutError(formatErrorMessage(reason, {\n            ...opts,\n            dynamic: (_opts_dynamic = opts == null ? void 0 : opts.dynamic) != null ? _opts_dynamic : \"error\"\n        }));\n    }\n    const message = formatErrorMessage(reason, {\n        ...opts,\n        // this error should be caught by Next to bail out of static generation\n        // in case it's uncaught, this link provides some additional context as to why\n        link: \"https://nextjs.org/docs/messages/dynamic-server-error\"\n    });\n    (0, _maybepostpone.maybePostpone)(staticGenerationStore, message);\n    // As this is a bailout, we don't want to revalidate, so set the revalidate\n    // to 0.\n    staticGenerationStore.revalidate = 0;\n    if (!(opts == null ? void 0 : opts.dynamic)) {\n        // we can statically prefetch pages that opt into dynamic,\n        // but not things like headers/cookies\n        staticGenerationStore.staticPrefetchBailout = true;\n    }\n    if (staticGenerationStore.isStaticGeneration) {\n        const err = new _hooksservercontext.DynamicServerError(message);\n        staticGenerationStore.dynamicUsageDescription = reason;\n        staticGenerationStore.dynamicUsageStack = err.stack;\n        throw err;\n    }\n    return false;\n};\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=static-generation-bailout.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js ***!
  \*****************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return StaticGenerationSearchParamsBailoutProvider;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _searchparamsbailoutproxy = __webpack_require__(/*! ./searchparams-bailout-proxy */ \"(app-pages-browser)/./node_modules/next/dist/client/components/searchparams-bailout-proxy.js\");\nfunction StaticGenerationSearchParamsBailoutProvider(param) {\n    let { Component, propsForComponent, isStaticGeneration } = param;\n    if (isStaticGeneration) {\n        const searchParams = (0, _searchparamsbailoutproxy.createSearchParamsBailoutProxy)();\n        return /*#__PURE__*/ _react.default.createElement(Component, {\n            searchParams: searchParams,\n            ...propsForComponent\n        });\n    }\n    return /*#__PURE__*/ _react.default.createElement(Component, propsForComponent);\n}\n_c = StaticGenerationSearchParamsBailoutProvider;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=static-generation-searchparams-bailout-provider.js.map\nvar _c;\n$RefreshReg$(_c, \"StaticGenerationSearchParamsBailoutProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js ***!
  \********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * Run function with `scroll-behavior: auto` applied to `<html/>`.\n * This css change will be reverted after the function finishes.\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"handleSmoothScroll\", ({\n    enumerable: true,\n    get: function() {\n        return handleSmoothScroll;\n    }\n}));\nfunction handleSmoothScroll(fn, options) {\n    if (options === void 0) options = {};\n    // if only the hash is changed, we don't need to disable smooth scrolling\n    // we only care to prevent smooth scrolling when navigating to a new page to avoid jarring UX\n    if (options.onlyHashChange) {\n        fn();\n        return;\n    }\n    const htmlElement = document.documentElement;\n    const existing = htmlElement.style.scrollBehavior;\n    htmlElement.style.scrollBehavior = \"auto\";\n    if (!options.dontForceLayout) {\n        // In Chrome-based browsers we need to force reflow before calling `scrollTo`.\n        // Otherwise it will not pickup the change in scrollBehavior\n        // More info here: https://github.com/vercel/next.js/issues/40719#issuecomment-1336248042\n        htmlElement.getClientRects();\n    }\n    fn();\n    htmlElement.style.scrollBehavior = existing;\n} //# sourceMappingURL=handle-smooth-scroll.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);