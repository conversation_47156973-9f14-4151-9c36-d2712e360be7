/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ctemp2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctemp2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ctemp2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctemp2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\temp2\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ctemp2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctemp2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q3RlbXAyJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDYXBwLXJvdXRlci5qcyZtb2R1bGVzPUQlM0ElNUN0ZW1wMiU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDY29tcG9uZW50cyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJm1vZHVsZXM9RCUzQSU1Q3RlbXAyJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPUQlM0ElNUN0ZW1wMiU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDY29tcG9uZW50cyU1Q25vdC1mb3VuZC1ib3VuZGFyeS5qcyZtb2R1bGVzPUQlM0ElNUN0ZW1wMiU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDY29tcG9uZW50cyU1Q3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMmbW9kdWxlcz1EJTNBJTVDdGVtcDIlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQTJHO0FBQzNHLDBPQUErRztBQUMvRyx3T0FBOEc7QUFDOUcsa1BBQW1IO0FBQ25ILHNRQUE2SDtBQUM3SCIsInNvdXJjZXMiOlsid2VicGFjazovL2ZpbGUtbWFuYWdlbWVudC1zeXN0ZW0vPzc2NDEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFx0ZW1wMlxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGFwcC1yb3V0ZXIuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHRlbXAyXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHRlbXAyXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcdGVtcDJcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxub3QtZm91bmQtYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHRlbXAyXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcdGVtcDJcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ctemp2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ctemp2%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=D%3A%5Ctemp2%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5Ctemp2%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=D%3A%5Ctemp2%5Csrc%5Ccontexts%5CThemeContext.tsx&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ctemp2%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=D%3A%5Ctemp2%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5Ctemp2%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=D%3A%5Ctemp2%5Csrc%5Ccontexts%5CThemeContext.tsx&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ThemeContext.tsx */ \"(ssr)/./src/contexts/ThemeContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q3RlbXAyJTVDbm9kZV9tb2R1bGVzJTVDcmVhY3QtaG90LXRvYXN0JTVDZGlzdCU1Q2luZGV4Lm1qcyZtb2R1bGVzPUQlM0ElNUN0ZW1wMiU1Q3NyYyU1Q2FwcCU1Q2dsb2JhbHMuY3NzJm1vZHVsZXM9RCUzQSU1Q3RlbXAyJTVDc3JjJTVDY29udGV4dHMlNUNBdXRoQ29udGV4dC50c3gmbW9kdWxlcz1EJTNBJTVDdGVtcDIlNUNzcmMlNUNjb250ZXh0cyU1Q1RoZW1lQ29udGV4dC50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNNQUE4RjtBQUM5Rix3S0FBOEU7QUFDOUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9maWxlLW1hbmFnZW1lbnQtc3lzdGVtLz8zMTEwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcdGVtcDJcXFxcbm9kZV9tb2R1bGVzXFxcXHJlYWN0LWhvdC10b2FzdFxcXFxkaXN0XFxcXGluZGV4Lm1qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcdGVtcDJcXFxcc3JjXFxcXGNvbnRleHRzXFxcXEF1dGhDb250ZXh0LnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcdGVtcDJcXFxcc3JjXFxcXGNvbnRleHRzXFxcXFRoZW1lQ29udGV4dC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ctemp2%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=D%3A%5Ctemp2%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5Ctemp2%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=D%3A%5Ctemp2%5Csrc%5Ccontexts%5CThemeContext.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ctemp2%5Csrc%5Capp%5Cpage.tsx&server=true!":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ctemp2%5Csrc%5Capp%5Cpage.tsx&server=true! ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q3RlbXAyJTVDc3JjJTVDYXBwJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmlsZS1tYW5hZ2VtZW50LXN5c3RlbS8/MzBjNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHRlbXAyXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ctemp2%5Csrc%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(ssr)/./src/contexts/ThemeContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Download_FileText_FolderOpen_Globe_Moon_Search_Shield_Sun_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,FileText,FolderOpen,Globe,Moon,Search,Shield,Sun,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Download_FileText_FolderOpen_Globe_Moon_Search_Shield_Sun_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,FileText,FolderOpen,Globe,Moon,Search,Shield,Sun,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Download_FileText_FolderOpen_Globe_Moon_Search_Shield_Sun_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,FileText,FolderOpen,Globe,Moon,Search,Shield,Sun,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_Download_FileText_FolderOpen_Globe_Moon_Search_Shield_Sun_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,FileText,FolderOpen,Globe,Moon,Search,Shield,Sun,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Download_FileText_FolderOpen_Globe_Moon_Search_Shield_Sun_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,FileText,FolderOpen,Globe,Moon,Search,Shield,Sun,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_FileText_FolderOpen_Globe_Moon_Search_Shield_Sun_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,FileText,FolderOpen,Globe,Moon,Search,Shield,Sun,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_FileText_FolderOpen_Globe_Moon_Search_Shield_Sun_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Download,FileText,FolderOpen,Globe,Moon,Search,Shield,Sun,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_FileText_FolderOpen_Globe_Moon_Search_Shield_Sun_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Download,FileText,FolderOpen,Globe,Moon,Search,Shield,Sun,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_Download_FileText_FolderOpen_Globe_Moon_Search_Shield_Sun_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Download,FileText,FolderOpen,Globe,Moon,Search,Shield,Sun,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Download_FileText_FolderOpen_Globe_Moon_Search_Shield_Sun_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Download,FileText,FolderOpen,Globe,Moon,Search,Shield,Sun,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction HomePage() {\n    const { user, profile, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { theme, language, toggleTheme, toggleLanguage } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__.useTheme)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && user && profile) {\n            // Redirect based on role\n            if (profile.role === \"admin\") {\n                router.push(\"/admin\");\n            } else {\n                router.push(\"/dashboard\");\n            }\n        }\n    }, [\n        user,\n        profile,\n        loading,\n        router\n    ]);\n    const features = [\n        {\n            icon: _barrel_optimize_names_Download_FileText_FolderOpen_Globe_Moon_Search_Shield_Sun_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: language === \"ar\" ? \"لوحة تحكم المشرف\" : \"Admin Dashboard\",\n            description: language === \"ar\" ? \"إدارة شاملة للمستخدمين والملفات مع صلاحيات متقدمة\" : \"Comprehensive user and file management with advanced permissions\"\n        },\n        {\n            icon: _barrel_optimize_names_Download_FileText_FolderOpen_Globe_Moon_Search_Shield_Sun_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: language === \"ar\" ? \"إدارة المستخدمين\" : \"User Management\",\n            description: language === \"ar\" ? \"إضافة وإدارة حسابات المستخدمين مع تحديد الصلاحيات والمساحة\" : \"Add and manage user accounts with permissions and storage quotas\"\n        },\n        {\n            icon: _barrel_optimize_names_Download_FileText_FolderOpen_Globe_Moon_Search_Shield_Sun_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            title: language === \"ar\" ? \"تنظيم الملفات\" : \"File Organization\",\n            description: language === \"ar\" ? \"تنظيم الملفات في مجلدات مخصصة مع نظام وسوم متقدم\" : \"Organize files in custom folders with advanced tagging system\"\n        },\n        {\n            icon: _barrel_optimize_names_Download_FileText_FolderOpen_Globe_Moon_Search_Shield_Sun_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            title: language === \"ar\" ? \"بحث ذكي\" : \"Smart Search\",\n            description: language === \"ar\" ? \"بحث متقدم حسب الاسم، النوع، الحجم، والتاريخ\" : \"Advanced search by name, type, size, and date\"\n        },\n        {\n            icon: _barrel_optimize_names_Download_FileText_FolderOpen_Globe_Moon_Search_Shield_Sun_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: language === \"ar\" ? \"رفع سهل\" : \"Easy Upload\",\n            description: language === \"ar\" ? \"رفع الملفات بالسحب والإفلات مع دعم أنواع متعددة\" : \"Drag and drop file upload with multiple format support\"\n        },\n        {\n            icon: _barrel_optimize_names_Download_FileText_FolderOpen_Globe_Moon_Search_Shield_Sun_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            title: language === \"ar\" ? \"مشاركة آمنة\" : \"Secure Sharing\",\n            description: language === \"ar\" ? \"مشاركة الملفات بروابط آمنة مع تحكم في الصلاحيات\" : \"Share files with secure links and permission controls\"\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-100 dark:from-secondary-900 dark:to-secondary-800\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"loading-spinner h-12 w-12 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-secondary-600 dark:text-secondary-400\",\n                        children: language === \"ar\" ? \"جاري التحميل...\" : \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100 dark:from-secondary-900 dark:to-secondary-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white/80 backdrop-blur-sm shadow-sm dark:bg-secondary-900/80\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_FileText_FolderOpen_Globe_Moon_Search_Shield_Sun_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-8 w-8 text-primary-700 dark:text-primary-400 ml-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-secondary-900 dark:text-secondary-100\",\n                                        children: language === \"ar\" ? \"نظام إدارة الملفات\" : \"File Management System\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleTheme,\n                                        className: \"p-2 rounded-lg hover:bg-secondary-100 dark:hover:bg-secondary-800 transition-colors\",\n                                        \"aria-label\": language === \"ar\" ? \"تغيير المظهر\" : \"Toggle theme\",\n                                        children: theme === \"light\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_FileText_FolderOpen_Globe_Moon_Search_Shield_Sun_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-5 w-5 text-secondary-600 dark:text-secondary-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_FileText_FolderOpen_Globe_Moon_Search_Shield_Sun_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5 text-secondary-600 dark:text-secondary-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleLanguage,\n                                        className: \"flex items-center gap-2 p-2 rounded-lg hover:bg-secondary-100 dark:hover:bg-secondary-800 transition-colors\",\n                                        \"aria-label\": language === \"ar\" ? \"تغيير اللغة\" : \"Toggle language\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_FileText_FolderOpen_Globe_Moon_Search_Shield_Sun_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-5 w-5 text-secondary-600 dark:text-secondary-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-secondary-600 dark:text-secondary-400\",\n                                                children: language === \"ar\" ? \"EN\" : \"عر\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                        href: \"/auth/login\",\n                                        className: \"btn btn-primary btn-md\",\n                                        children: language === \"ar\" ? \"تسجيل الدخول\" : \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16 animate-fade-in\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl md:text-6xl font-bold text-secondary-900 dark:text-secondary-100 mb-6\",\n                                children: language === \"ar\" ? \"مرحباً بك في نظام إدارة الملفات\" : \"Welcome to File Management System\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-secondary-600 dark:text-secondary-400 max-w-3xl mx-auto mb-8\",\n                                children: language === \"ar\" ? \"نظام شامل لإدارة وتنظيم الملفات مع إمكانيات متقدمة للمشرفين والمستخدمين\" : \"A comprehensive system for managing and organizing files with advanced features for admins and users\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                        href: \"/auth/register\",\n                                        className: \"btn btn-primary btn-lg\",\n                                        children: language === \"ar\" ? \"إنشاء حساب جديد\" : \"Create Account\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                        href: \"/auth/login\",\n                                        className: \"btn btn-secondary btn-lg\",\n                                        children: language === \"ar\" ? \"تسجيل الدخول\" : \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\",\n                        children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card hover:shadow-lg transition-all duration-300 animate-slide-up\",\n                                style: {\n                                    animationDelay: `${index * 100}ms`\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                            className: \"h-12 w-12 text-primary-600 dark:text-primary-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-secondary-900 dark:text-secondary-100 mb-2\",\n                                            children: feature.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-secondary-600 dark:text-secondary-400\",\n                                            children: feature.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this)\n                            }, index, false, {\n                                fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center bg-white/50 dark:bg-secondary-800/50 backdrop-blur-sm rounded-2xl p-8 animate-fade-in\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-secondary-900 dark:text-secondary-100 mb-4\",\n                                children: language === \"ar\" ? \"ابدأ الآن\" : \"Get Started Now\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-secondary-600 dark:text-secondary-400 mb-6\",\n                                children: language === \"ar\" ? \"انضم إلى آلاف المستخدمين الذين يثقون في نظامنا لإدارة ملفاتهم\" : \"Join thousands of users who trust our system to manage their files\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                href: \"/auth/register\",\n                                className: \"btn btn-primary btn-lg\",\n                                children: language === \"ar\" ? \"ابدأ مجاناً\" : \"Start Free\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-secondary-900 dark:bg-secondary-950 text-white py-8 mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-secondary-300\",\n                        children: language === \"ar\" ? \"\\xa9 2024 نظام إدارة الملفات. جميع الحقوق محفوظة.\" : \"\\xa9 2024 File Management System. All rights reserved.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\temp2\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createSupabaseClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get initial session\n        const getInitialSession = async ()=>{\n            const { data: { session } } = await supabase.auth.getSession();\n            if (session?.user) {\n                setUser(session.user);\n                await fetchProfile(session.user.id);\n            }\n            setLoading(false);\n        };\n        getInitialSession();\n        // Listen for auth changes\n        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session)=>{\n            if (session?.user) {\n                setUser(session.user);\n                await fetchProfile(session.user.id);\n            } else {\n                setUser(null);\n                setProfile(null);\n            }\n            setLoading(false);\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        supabase\n    ]);\n    const fetchProfile = async (userId)=>{\n        try {\n            const { data, error } = await supabase.from(\"profiles\").select(\"*\").eq(\"id\", userId).single();\n            if (error) {\n                console.error(\"Error fetching profile:\", error);\n                return;\n            }\n            setProfile(data);\n        } catch (error) {\n            console.error(\"Error fetching profile:\", error);\n        }\n    };\n    const signIn = async (email, password)=>{\n        const { error } = await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        return {\n            error\n        };\n    };\n    const signUp = async (email, password, fullName)=>{\n        const { error } = await supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    full_name: fullName\n                }\n            }\n        });\n        return {\n            error\n        };\n    };\n    const signOut = async ()=>{\n        await supabase.auth.signOut();\n    };\n    const updateProfile = async (updates)=>{\n        if (!user) return {\n            error: new Error(\"No user logged in\")\n        };\n        const { error } = await supabase.from(\"profiles\").update(updates).eq(\"id\", user.id);\n        if (!error) {\n            setProfile((prev)=>prev ? {\n                    ...prev,\n                    ...updates\n                } : null);\n        }\n        return {\n            error\n        };\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            profile,\n            loading,\n            signIn,\n            signUp,\n            signOut,\n            updateProfile\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\temp2\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"ar\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Load theme from localStorage\n        const savedTheme = localStorage.getItem(\"theme\");\n        const savedLanguage = localStorage.getItem(\"language\");\n        if (savedTheme) {\n            setTheme(savedTheme);\n        } else {\n            // Check system preference\n            const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n            setTheme(systemTheme);\n        }\n        if (savedLanguage) {\n            setLanguage(savedLanguage);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Apply theme to document\n        const root = document.documentElement;\n        if (theme === \"dark\") {\n            root.classList.add(\"dark\");\n        } else {\n            root.classList.remove(\"dark\");\n        }\n        // Apply language direction\n        if (language === \"ar\") {\n            root.setAttribute(\"dir\", \"rtl\");\n            root.setAttribute(\"lang\", \"ar\");\n        } else {\n            root.setAttribute(\"dir\", \"ltr\");\n            root.setAttribute(\"lang\", \"en\");\n        }\n        localStorage.setItem(\"theme\", theme);\n        localStorage.setItem(\"language\", language);\n    }, [\n        theme,\n        language\n    ]);\n    const toggleTheme = ()=>{\n        setTheme((prev)=>prev === \"light\" ? \"dark\" : \"light\");\n    };\n    const toggleLanguage = ()=>{\n        setLanguage((prev)=>prev === \"ar\" ? \"en\" : \"ar\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            language,\n            toggleTheme,\n            toggleLanguage,\n            setTheme,\n            setLanguage\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\temp2\\\\src\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseClient: () => (/* binding */ createSupabaseClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n\n\nconst supabaseUrl = \"https://cxggygsjzvjexizmvqsg.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImN4Z2d5Z3NqenZqZXhpem12cXNnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1NzMwMDgsImV4cCI6MjA2ODE0OTAwOH0.rAtRSm6PbJZMvRDUVHSKoLkjgUNf0ELNfNxDgeLnf1g\";\n// Client-side Supabase client\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n// Client component Supabase client\nconst createSupabaseClient = ()=>(0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"60a6c172e717\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmlsZS1tYW5hZ2VtZW50LXN5c3RlbS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/Y2ZhZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjYwYTZjMTcyZTcxN1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(rsc)/./src/contexts/ThemeContext.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"نظام إدارة الملفات | File Management System\",\n    description: \"نظام شامل لإدارة وتنظيم الملفات مع إمكانيات متقدمة للمشرفين والمستخدمين\",\n    keywords: \"إدارة الملفات, تخزين سحابي, مشاركة الملفات, تنظيم المجلدات\",\n    authors: [\n        {\n            name: \"File Management System\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"index, follow\",\n    openGraph: {\n        title: \"نظام إدارة الملفات\",\n        description: \"نظام شامل لإدارة وتنظيم الملفات\",\n        type: \"website\",\n        locale: \"ar_SA\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\temp2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\temp2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#2563eb\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\temp2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\temp2\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"font-arabic min-h-screen antialiased\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.AuthProvider, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex min-h-screen flex-col\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                    className: \"flex-1\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\temp2\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\temp2\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                                position: \"top-center\",\n                                toastOptions: {\n                                    duration: 4000,\n                                    style: {\n                                        background: \"var(--background)\",\n                                        color: \"var(--foreground)\",\n                                        border: \"1px solid var(--border)\"\n                                    },\n                                    success: {\n                                        iconTheme: {\n                                            primary: \"#22c55e\",\n                                            secondary: \"#ffffff\"\n                                        }\n                                    },\n                                    error: {\n                                        iconTheme: {\n                                            primary: \"#ef4444\",\n                                            secondary: \"#ffffff\"\n                                        }\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\temp2\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\temp2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\temp2\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\temp2\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\temp2\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\temp2\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\temp2\src\contexts\AuthContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\temp2\src\contexts\AuthContext.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\temp2\src\contexts\AuthContext.tsx#useAuth`);


/***/ }),

/***/ "(rsc)/./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0),
/* harmony export */   useTheme: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\temp2\src\contexts\ThemeContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\temp2\src\contexts\ThemeContext.tsx#ThemeProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\temp2\src\contexts\ThemeContext.tsx#useTheme`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/jose","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/react-hot-toast","vendor-chunks/set-cookie-parser","vendor-chunks/webidl-conversions","vendor-chunks/goober","vendor-chunks/isows","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ctemp2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctemp2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();