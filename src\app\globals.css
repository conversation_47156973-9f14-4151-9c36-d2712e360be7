@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 248, 250, 252;
  --background-end-rgb: 241, 245, 249;
}

[data-theme="dark"] {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 15, 23, 42;
  --background-end-rgb: 30, 41, 59;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
  font-family: 'Cairo', 'Tajawal', sans-serif;
  direction: rtl;
}

[lang="en"] body {
  font-family: 'Inter', system-ui, sans-serif;
  direction: ltr;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* Custom Components */
@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 dark:bg-primary-700 dark:hover:bg-primary-600;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-100 text-secondary-900 hover:bg-secondary-200 dark:bg-secondary-800 dark:text-secondary-100 dark:hover:bg-secondary-700;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700;
  }
  
  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700;
  }
  
  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700;
  }
  
  .btn-ghost {
    @apply btn hover:bg-secondary-100 dark:hover:bg-secondary-800;
  }
  
  .btn-sm {
    @apply h-8 px-3 text-xs;
  }
  
  .btn-md {
    @apply h-10 px-4 py-2;
  }
  
  .btn-lg {
    @apply h-12 px-8 text-base;
  }
  
  .input {
    @apply flex h-10 w-full rounded-md border border-secondary-300 bg-white px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-secondary-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-secondary-700 dark:bg-secondary-900 dark:text-secondary-100 dark:placeholder:text-secondary-400;
  }
  
  .card {
    @apply rounded-lg border border-secondary-200 bg-white p-6 shadow-sm dark:border-secondary-800 dark:bg-secondary-900;
  }
  
  .sidebar-item {
    @apply flex items-center gap-3 rounded-lg px-3 py-2 text-secondary-700 transition-all hover:bg-secondary-100 dark:text-secondary-300 dark:hover:bg-secondary-800;
  }
  
  .sidebar-item.active {
    @apply bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300;
  }
  
  .dropdown {
    @apply absolute right-0 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-secondary-800 dark:ring-secondary-700;
  }
  
  .dropdown-item {
    @apply block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100 dark:text-secondary-300 dark:hover:bg-secondary-700;
  }
  
  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-300;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-300;
  }
  
  .badge-danger {
    @apply badge bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-300;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-secondary-300 border-t-primary-600;
  }
}

/* Animations */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
}

/* File upload styles */
.file-upload-area {
  @apply border-2 border-dashed border-secondary-300 rounded-lg p-8 text-center transition-colors hover:border-primary-400 dark:border-secondary-700 dark:hover:border-primary-600;
}

.file-upload-area.dragover {
  @apply border-primary-500 bg-primary-50 dark:bg-primary-950;
}

/* Grid layouts */
.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

/* Text utilities */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}
