# إعداد Supabase لنظام إدارة الملفات

## الخطوة 1: إنشاء مشروع Supabase

1. اذهب إلى [supabase.com](https://supabase.com)
2. سجل الدخول أو أنشئ حساب جديد
3. انق<PERSON> على "New Project"
4. اختر Organization أو أنشئ واحدة جديدة
5. أدخل اسم المشروع (مثل: file-management-system)
6. أدخل كلمة مرور قوية لقاعدة البيانات
7. اختر المنطقة الأقرب لك
8. انقر على "Create new project"

## الخطوة 2: الحصول على مفاتيح API

1. بعد إنشاء المشروع، اذهب إلى Settings > API
2. انسخ المعلومات التالية:
   - **Project URL**: سيكون شكله `https://your-project-id.supabase.co`
   - **anon public key**: المفتاح العام
   - **service_role key**: مفتاح الخدمة (احتفظ به سرياً)

## الخطوة 3: تحديث ملف البيئة

1. انسخ الملف `.env.example` إلى `.env.local`
2. استبدل القيم بالمعلومات من مشروعك:

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=any_random_string_here
```

## الخطوة 4: إعداد قاعدة البيانات

1. في لوحة تحكم Supabase، اذهب إلى SQL Editor
2. انسخ محتوى ملف `supabase-setup.sql`
3. الصق الكود في محرر SQL
4. انقر على "Run" لتنفيذ الكود

هذا سينشئ:
- الجداول المطلوبة (profiles, categories, files, file_downloads)
- سياسات الأمان (RLS Policies)
- المحفزات (Triggers)
- دلو التخزين للملفات

## الخطوة 5: إعداد التخزين

1. اذهب إلى Storage في لوحة تحكم Supabase
2. تأكد من وجود bucket باسم "files"
3. إذا لم يكن موجوداً، أنشئه يدوياً:
   - انقر على "New bucket"
   - اسم البucket: `files`
   - اجعله Private (غير عام)

## الخطوة 6: إنشاء مستخدم أدمن

يمكنك إنشاء مستخدم أدمن بطريقتين:

### الطريقة الأولى: من خلال Authentication UI
1. اذهب إلى Authentication > Users
2. انقر على "Add user"
3. أدخل البريد الإلكتروني وكلمة المرور
4. بعد إنشاء المستخدم، اذهب إلى Table Editor > profiles
5. ابحث عن المستخدم وغير role إلى 'admin'

### الطريقة الثانية: من خلال SQL
```sql
-- أدخل بريدك الإلكتروني وكلمة المرور المرغوبة
INSERT INTO auth.users (
  instance_id,
  id,
  aud,
  role,
  email,
  encrypted_password,
  email_confirmed_at,
  recovery_sent_at,
  last_sign_in_at,
  raw_app_meta_data,
  raw_user_meta_data,
  created_at,
  updated_at,
  confirmation_token,
  email_change,
  email_change_token_new,
  recovery_token
) VALUES (
  '00000000-0000-0000-0000-000000000000',
  gen_random_uuid(),
  'authenticated',
  'authenticated',
  '<EMAIL>', -- غير هذا إلى بريدك
  crypt('admin123', gen_salt('bf')), -- غير كلمة المرور
  NOW(),
  NOW(),
  NOW(),
  '{"provider":"email","providers":["email"]}',
  '{}',
  NOW(),
  NOW(),
  '',
  '',
  '',
  ''
);

-- ثم حدث الدور في جدول profiles
UPDATE profiles SET role = 'admin' WHERE email = '<EMAIL>';
```

## الخطوة 7: اختبار الإعداد

1. شغل المشروع: `npm run dev`
2. اذهب إلى `http://localhost:3000`
3. جرب تسجيل الدخول كأدمن
4. تأكد من عمل جميع الوظائف

## استكشاف الأخطاء

### خطأ "Invalid URL"
- تأكد من أن NEXT_PUBLIC_SUPABASE_URL يبدأ بـ https://
- تأكد من عدم وجود مسافات في بداية أو نهاية URL

### خطأ "Invalid API key"
- تأكد من نسخ المفاتيح بشكل صحيح
- تأكد من عدم وجود مسافات إضافية

### مشاكل في الصلاحيات
- تأكد من تشغيل جميع أوامر SQL في supabase-setup.sql
- تحقق من أن RLS مفعل على جميع الجداول

### مشاكل رفع الملفات
- تأكد من وجود bucket "files"
- تحقق من سياسات Storage

## الأمان

⚠️ **مهم**: 
- لا تشارك service_role_key مع أحد
- استخدم كلمات مرور قوية
- فعل 2FA على حساب Supabase
- راجع سياسات الأمان بانتظام

## الدعم

إذا واجهت مشاكل:
1. راجع [وثائق Supabase](https://supabase.com/docs)
2. تحقق من logs في لوحة تحكم Supabase
3. تأكد من إعدادات البيئة
