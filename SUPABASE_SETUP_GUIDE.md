# 🚀 دليل إعداد Supabase - نظام إدارة الملفات

## الخطوة 1: إعداد المشروع في Supabase

### 1.1 إنشاء المشروع
1. اذهب إلى [supabase.com](https://supabase.com)
2. سجل الدخول أو أنشئ حساب جديد
3. انقر على "New Project"
4. املأ البيانات:
   - **اسم المشروع**: File Management System
   - **كلمة مرور قاعدة البيانات**: اختر كلمة مرور قوية
   - **المنطقة**: اختر الأقرب لك
5. انقر على "Create new project"

### 1.2 الحصول على مفاتيح API
1. بعد إنشاء المشروع، اذهب إلى **Settings > API**
2. انسخ المعلومات التالية:
   - **Project URL**: `https://your-project-id.supabase.co`
   - **anon public key**: المفتاح العام
   - **service_role key**: مفتاح الخدمة (احتفظ به سرياً)

## الخطوة 2: تحديث ملف البيئة

1. في مجلد المشروع، انسخ `.env.example` إلى `.env.local`
2. استبدل القيم بمعلومات مشروعك:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# App Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=any_random_string_32_chars_long

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif,mp4,mp3,wav
```

## الخطوة 3: إعداد قاعدة البيانات

### 3.1 تشغيل SQL Script
1. في لوحة تحكم Supabase، اذهب إلى **SQL Editor**
2. انقر على "New query"
3. انسخ محتوى ملف `supabase-setup.sql` بالكامل
4. الصق الكود في المحرر
5. انقر على **"Run"** لتنفيذ الكود

### 3.2 التحقق من إنشاء الجداول
1. اذهب إلى **Table Editor**
2. تأكد من وجود الجداول التالية:
   - `profiles`
   - `folders`
   - `files`
   - `file_shares`
   - `downloads`

## الخطوة 4: إعداد التخزين (Storage)

### 4.1 إنشاء Bucket
1. اذهب إلى **Storage**
2. انقر على "New bucket"
3. املأ البيانات:
   - **Name**: `files`
   - **Public**: اتركه غير مفعل (Private)
4. انقر على "Create bucket"

### 4.2 التحقق من السياسات
1. انقر على bucket "files"
2. اذهب إلى **Policies**
3. تأكد من وجود السياسات التي تم إنشاؤها بواسطة SQL script

## الخطوة 5: إنشاء المستخدمين التجريبيين

### 5.1 إنشاء مستخدم مشرف
1. اذهب إلى **Authentication > Users**
2. انقر على "Add user"
3. املأ البيانات:
   - **Email**: `<EMAIL>`
   - **Password**: `Admin123!`
   - **Auto Confirm User**: مفعل
4. انقر على "Create user"

### 5.2 إنشاء مستخدم عادي
1. انقر على "Add user" مرة أخرى
2. املأ البيانات:
   - **Email**: `<EMAIL>`
   - **Password**: `User123!`
   - **Auto Confirm User**: مفعل
3. انقر على "Create user"

### 5.3 تحديث أدوار المستخدمين
1. اذهب إلى **Table Editor > profiles**
2. ابحث عن المستخدم `<EMAIL>`
3. غير `role` إلى `admin`
4. احفظ التغييرات

## الخطوة 6: اختبار النظام

### 6.1 تشغيل المشروع
```bash
npm run dev
```

### 6.2 اختبار تسجيل الدخول
1. اذهب إلى `http://localhost:3000`
2. انقر على "تسجيل الدخول"
3. جرب الحسابات التجريبية:
   - **مشرف**: `<EMAIL>` / `Admin123!`
   - **مستخدم**: `<EMAIL>` / `User123!`

## الخطوة 7: إعدادات إضافية (اختيارية)

### 7.1 تخصيص إعدادات المصادقة
1. اذهب إلى **Authentication > Settings**
2. في **Site URL**، أضف: `http://localhost:3000`
3. في **Redirect URLs**، أضف:
   - `http://localhost:3000/auth/callback`
   - `http://localhost:3000/dashboard`
   - `http://localhost:3000/admin`

### 7.2 تفعيل Email Templates (اختياري)
1. اذهب إلى **Authentication > Email Templates**
2. خصص رسائل البريد الإلكتروني حسب الحاجة

## 🔧 استكشاف الأخطاء

### خطأ "Invalid URL"
- تأكد من أن `NEXT_PUBLIC_SUPABASE_URL` يبدأ بـ `https://`
- تأكد من عدم وجود مسافات في بداية أو نهاية URL

### خطأ "Invalid API key"
- تأكد من نسخ المفاتيح بشكل صحيح من Supabase
- تأكد من عدم وجود مسافات إضافية

### مشاكل في رفع الملفات
- تأكد من إنشاء bucket "files"
- تحقق من سياسات Storage
- تأكد من أن RLS مفعل

### مشاكل في تسجيل الدخول
- تأكد من تشغيل جميع أوامر SQL
- تحقق من إنشاء المستخدمين في Authentication
- تأكد من تحديث أدوار المستخدمين في جدول profiles

## 📞 الحصول على المساعدة

إذا واجهت مشاكل:
1. راجع [وثائق Supabase](https://supabase.com/docs)
2. تحقق من **Logs** في لوحة تحكم Supabase
3. راجع **Console** في المتصفح للأخطاء
4. تأكد من صحة جميع متغيرات البيئة

## ✅ قائمة التحقق النهائية

- [ ] تم إنشاء مشروع Supabase
- [ ] تم نسخ مفاتيح API إلى `.env.local`
- [ ] تم تشغيل SQL script بنجاح
- [ ] تم إنشاء bucket "files"
- [ ] تم إنشاء المستخدمين التجريبيين
- [ ] تم تحديث أدوار المستخدمين
- [ ] تم اختبار تسجيل الدخول
- [ ] يعمل رفع الملفات بشكل صحيح

---

**🎉 تهانينا! نظام إدارة الملفات جاهز للاستخدام!**
