"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/@supabase/auth-helpers-nextjs/dist/index.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all)=>{\n    for(var name in all)__defProp(target, name, {\n        get: all[name],\n        enumerable: true\n    });\n};\nvar __copyProps = (to, from, except, desc)=>{\n    if (from && typeof from === \"object\" || typeof from === \"function\") {\n        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {\n            get: ()=>from[key],\n            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable\n        });\n    }\n    return to;\n};\nvar __toCommonJS = (mod)=>__copyProps(__defProp({}, \"__esModule\", {\n        value: true\n    }), mod);\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n    createBrowserSupabaseClient: ()=>createBrowserSupabaseClient,\n    createClientComponentClient: ()=>createClientComponentClient,\n    createMiddlewareClient: ()=>createMiddlewareClient,\n    createMiddlewareSupabaseClient: ()=>createMiddlewareSupabaseClient,\n    createPagesBrowserClient: ()=>createPagesBrowserClient,\n    createPagesServerClient: ()=>createPagesServerClient,\n    createRouteHandlerClient: ()=>createRouteHandlerClient,\n    createServerActionClient: ()=>createServerActionClient,\n    createServerComponentClient: ()=>createServerComponentClient,\n    createServerSupabaseClient: ()=>createServerSupabaseClient\n});\nmodule.exports = __toCommonJS(src_exports);\n// src/clientComponentClient.ts\nvar import_auth_helpers_shared = __webpack_require__(/*! @supabase/auth-helpers-shared */ \"(middleware)/./node_modules/@supabase/auth-helpers-shared/dist/index.mjs\");\nvar supabase;\nfunction createClientComponentClient({ supabaseUrl = \"https://your-project.supabase.co\", supabaseKey = \"your_supabase_anon_key\", options, cookieOptions, isSingleton = true } = {}) {\n    if (!supabaseUrl || !supabaseKey) {\n        throw new Error(\"either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!\");\n    }\n    const createNewClient = ()=>{\n        var _a;\n        return (0, import_auth_helpers_shared.createSupabaseClient)(supabaseUrl, supabaseKey, {\n            ...options,\n            global: {\n                ...options == null ? void 0 : options.global,\n                headers: {\n                    ...(_a = options == null ? void 0 : options.global) == null ? void 0 : _a.headers,\n                    \"X-Client-Info\": `${\"@supabase/auth-helpers-nextjs\"}@${\"0.8.7\"}`\n                }\n            },\n            auth: {\n                storage: new import_auth_helpers_shared.BrowserCookieAuthStorageAdapter(cookieOptions)\n            }\n        });\n    };\n    if (isSingleton) {\n        const _supabase = supabase ?? createNewClient();\n        if (true) return _supabase;\n        if (!supabase) supabase = _supabase;\n        return supabase;\n    }\n    return createNewClient();\n}\n// src/pagesBrowserClient.ts\nvar createPagesBrowserClient = createClientComponentClient;\n// src/pagesServerClient.ts\nvar import_auth_helpers_shared2 = __webpack_require__(/*! @supabase/auth-helpers-shared */ \"(middleware)/./node_modules/@supabase/auth-helpers-shared/dist/index.mjs\");\nvar import_set_cookie_parser = __webpack_require__(/*! set-cookie-parser */ \"(middleware)/./node_modules/set-cookie-parser/lib/set-cookie.js\");\nvar NextServerAuthStorageAdapter = class extends import_auth_helpers_shared2.CookieAuthStorageAdapter {\n    constructor(context, cookieOptions){\n        super(cookieOptions);\n        this.context = context;\n    }\n    getCookie(name) {\n        var _a, _b, _c;\n        const setCookie = (0, import_set_cookie_parser.splitCookiesString)(((_b = (_a = this.context.res) == null ? void 0 : _a.getHeader(\"set-cookie\")) == null ? void 0 : _b.toString()) ?? \"\").map((c)=>(0, import_auth_helpers_shared2.parseCookies)(c)[name]).find((c)=>!!c);\n        const value = setCookie ?? ((_c = this.context.req) == null ? void 0 : _c.cookies[name]);\n        return value;\n    }\n    setCookie(name, value) {\n        this._setCookie(name, value);\n    }\n    deleteCookie(name) {\n        this._setCookie(name, \"\", {\n            maxAge: 0\n        });\n    }\n    _setCookie(name, value, options) {\n        var _a;\n        const setCookies = (0, import_set_cookie_parser.splitCookiesString)(((_a = this.context.res.getHeader(\"set-cookie\")) == null ? void 0 : _a.toString()) ?? \"\").filter((c)=>!(name in (0, import_auth_helpers_shared2.parseCookies)(c)));\n        const cookieStr = (0, import_auth_helpers_shared2.serializeCookie)(name, value, {\n            ...this.cookieOptions,\n            ...options,\n            // Allow supabase-js on the client to read the cookie as well\n            httpOnly: false\n        });\n        this.context.res.setHeader(\"set-cookie\", [\n            ...setCookies,\n            cookieStr\n        ]);\n    }\n};\nfunction createPagesServerClient(context, { supabaseUrl = \"https://your-project.supabase.co\", supabaseKey = \"your_supabase_anon_key\", options, cookieOptions } = {}) {\n    var _a;\n    if (!supabaseUrl || !supabaseKey) {\n        throw new Error(\"either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!\");\n    }\n    return (0, import_auth_helpers_shared2.createSupabaseClient)(supabaseUrl, supabaseKey, {\n        ...options,\n        global: {\n            ...options == null ? void 0 : options.global,\n            headers: {\n                ...(_a = options == null ? void 0 : options.global) == null ? void 0 : _a.headers,\n                \"X-Client-Info\": `${\"@supabase/auth-helpers-nextjs\"}@${\"0.8.7\"}`\n            }\n        },\n        auth: {\n            storage: new NextServerAuthStorageAdapter(context, cookieOptions)\n        }\n    });\n}\n// src/middlewareClient.ts\nvar import_auth_helpers_shared3 = __webpack_require__(/*! @supabase/auth-helpers-shared */ \"(middleware)/./node_modules/@supabase/auth-helpers-shared/dist/index.mjs\");\nvar import_set_cookie_parser2 = __webpack_require__(/*! set-cookie-parser */ \"(middleware)/./node_modules/set-cookie-parser/lib/set-cookie.js\");\nvar NextMiddlewareAuthStorageAdapter = class extends import_auth_helpers_shared3.CookieAuthStorageAdapter {\n    constructor(context, cookieOptions){\n        super(cookieOptions);\n        this.context = context;\n    }\n    getCookie(name) {\n        var _a;\n        const setCookie = (0, import_set_cookie_parser2.splitCookiesString)(((_a = this.context.res.headers.get(\"set-cookie\")) == null ? void 0 : _a.toString()) ?? \"\").map((c)=>(0, import_auth_helpers_shared3.parseCookies)(c)[name]).find((c)=>!!c);\n        if (setCookie) {\n            return setCookie;\n        }\n        const cookies = (0, import_auth_helpers_shared3.parseCookies)(this.context.req.headers.get(\"cookie\") ?? \"\");\n        return cookies[name];\n    }\n    setCookie(name, value) {\n        this._setCookie(name, value);\n    }\n    deleteCookie(name) {\n        this._setCookie(name, \"\", {\n            maxAge: 0\n        });\n    }\n    _setCookie(name, value, options) {\n        const newSessionStr = (0, import_auth_helpers_shared3.serializeCookie)(name, value, {\n            ...this.cookieOptions,\n            ...options,\n            // Allow supabase-js on the client to read the cookie as well\n            httpOnly: false\n        });\n        if (this.context.res.headers) {\n            this.context.res.headers.append(\"set-cookie\", newSessionStr);\n        }\n    }\n};\nfunction createMiddlewareClient(context, { supabaseUrl = \"https://your-project.supabase.co\", supabaseKey = \"your_supabase_anon_key\", options, cookieOptions } = {}) {\n    var _a;\n    if (!supabaseUrl || !supabaseKey) {\n        throw new Error(\"either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!\");\n    }\n    return (0, import_auth_helpers_shared3.createSupabaseClient)(supabaseUrl, supabaseKey, {\n        ...options,\n        global: {\n            ...options == null ? void 0 : options.global,\n            headers: {\n                ...(_a = options == null ? void 0 : options.global) == null ? void 0 : _a.headers,\n                \"X-Client-Info\": `${\"@supabase/auth-helpers-nextjs\"}@${\"0.8.7\"}`\n            }\n        },\n        auth: {\n            storage: new NextMiddlewareAuthStorageAdapter(context, cookieOptions)\n        }\n    });\n}\n// src/serverComponentClient.ts\nvar import_auth_helpers_shared4 = __webpack_require__(/*! @supabase/auth-helpers-shared */ \"(middleware)/./node_modules/@supabase/auth-helpers-shared/dist/index.mjs\");\nvar NextServerComponentAuthStorageAdapter = class extends import_auth_helpers_shared4.CookieAuthStorageAdapter {\n    constructor(context, cookieOptions){\n        super(cookieOptions);\n        this.context = context;\n    }\n    getCookie(name) {\n        var _a;\n        const nextCookies = this.context.cookies();\n        return (_a = nextCookies.get(name)) == null ? void 0 : _a.value;\n    }\n    setCookie(name, value) {}\n    deleteCookie(name) {}\n};\nfunction createServerComponentClient(context, { supabaseUrl = \"https://your-project.supabase.co\", supabaseKey = \"your_supabase_anon_key\", options, cookieOptions } = {}) {\n    var _a;\n    if (!supabaseUrl || !supabaseKey) {\n        throw new Error(\"either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!\");\n    }\n    return (0, import_auth_helpers_shared4.createSupabaseClient)(supabaseUrl, supabaseKey, {\n        ...options,\n        global: {\n            ...options == null ? void 0 : options.global,\n            headers: {\n                ...(_a = options == null ? void 0 : options.global) == null ? void 0 : _a.headers,\n                \"X-Client-Info\": `${\"@supabase/auth-helpers-nextjs\"}@${\"0.8.7\"}`\n            }\n        },\n        auth: {\n            storage: new NextServerComponentAuthStorageAdapter(context, cookieOptions)\n        }\n    });\n}\n// src/routeHandlerClient.ts\nvar import_auth_helpers_shared5 = __webpack_require__(/*! @supabase/auth-helpers-shared */ \"(middleware)/./node_modules/@supabase/auth-helpers-shared/dist/index.mjs\");\nvar NextRouteHandlerAuthStorageAdapter = class extends import_auth_helpers_shared5.CookieAuthStorageAdapter {\n    constructor(context, cookieOptions){\n        super(cookieOptions);\n        this.context = context;\n    }\n    getCookie(name) {\n        var _a;\n        const nextCookies = this.context.cookies();\n        return (_a = nextCookies.get(name)) == null ? void 0 : _a.value;\n    }\n    setCookie(name, value) {\n        const nextCookies = this.context.cookies();\n        nextCookies.set(name, value, this.cookieOptions);\n    }\n    deleteCookie(name) {\n        const nextCookies = this.context.cookies();\n        nextCookies.set(name, \"\", {\n            ...this.cookieOptions,\n            maxAge: 0\n        });\n    }\n};\nfunction createRouteHandlerClient(context, { supabaseUrl = \"https://your-project.supabase.co\", supabaseKey = \"your_supabase_anon_key\", options, cookieOptions } = {}) {\n    var _a;\n    if (!supabaseUrl || !supabaseKey) {\n        throw new Error(\"either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!\");\n    }\n    return (0, import_auth_helpers_shared5.createSupabaseClient)(supabaseUrl, supabaseKey, {\n        ...options,\n        global: {\n            ...options == null ? void 0 : options.global,\n            headers: {\n                ...(_a = options == null ? void 0 : options.global) == null ? void 0 : _a.headers,\n                \"X-Client-Info\": `${\"@supabase/auth-helpers-nextjs\"}@${\"0.8.7\"}`\n            }\n        },\n        auth: {\n            storage: new NextRouteHandlerAuthStorageAdapter(context, cookieOptions)\n        }\n    });\n}\n// src/serverActionClient.ts\nvar createServerActionClient = createRouteHandlerClient;\n// src/deprecated.ts\nfunction createBrowserSupabaseClient({ supabaseUrl = \"https://your-project.supabase.co\", supabaseKey = \"your_supabase_anon_key\", options, cookieOptions } = {}) {\n    console.warn(\"Please utilize the `createPagesBrowserClient` function instead of the deprecated `createBrowserSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages\");\n    return createPagesBrowserClient({\n        supabaseUrl,\n        supabaseKey,\n        options,\n        cookieOptions\n    });\n}\nfunction createServerSupabaseClient(context, { supabaseUrl = \"https://your-project.supabase.co\", supabaseKey = \"your_supabase_anon_key\", options, cookieOptions } = {}) {\n    console.warn(\"Please utilize the `createPagesServerClient` function instead of the deprecated `createServerSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages\");\n    return createPagesServerClient(context, {\n        supabaseUrl,\n        supabaseKey,\n        options,\n        cookieOptions\n    });\n}\nfunction createMiddlewareSupabaseClient(context, { supabaseUrl = \"https://your-project.supabase.co\", supabaseKey = \"your_supabase_anon_key\", options, cookieOptions } = {}) {\n    console.warn(\"Please utilize the `createMiddlewareClient` function instead of the deprecated `createMiddlewareSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs#middleware\");\n    return createMiddlewareClient(context, {\n        supabaseUrl,\n        supabaseKey,\n        options,\n        cookieOptions\n    });\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0); //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\n");

/***/ })

});