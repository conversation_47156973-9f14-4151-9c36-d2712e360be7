{"version": 3, "sources": ["../../../src/telemetry/events/swc-load-failure.ts"], "names": ["eventSwcLoadFailure", "EVENT_PLUGIN_PRESENT", "event", "telemetry", "traceGlobals", "get", "glibcVersion", "installedSwcPackages", "process", "report", "getReport", "header", "glibcVersionRuntime", "pkgNames", "Object", "keys", "optionalDependencies", "filter", "pkg", "startsWith", "installedPkgs", "version", "require", "push", "length", "sort", "join", "record", "eventName", "payload", "nextVersion", "arch", "platform", "nodeVersion", "versions", "node", "wasm", "nativeBindingsErrorCode", "flush"], "mappings": ";;;;+BAoBsBA;;;eAAAA;;;wBApBO;6BAGgC;AAE7D,MAAMC,uBAAuB;AAetB,eAAeD,oBACpBE,KAAsC;IAEtC,MAAMC,YAAmCC,oBAAY,CAACC,GAAG,CAAC;IAC1D,wCAAwC;IACxC,IAAI,CAACF,WAAW;IAEhB,IAAIG;IACJ,IAAIC;IAEJ,IAAI;YAEaC;QADf,aAAa;QACbF,gBAAeE,kBAAAA,QAAQC,MAAM,qBAAdD,gBAAgBE,SAAS,GAAGC,MAAM,CAACC,mBAAmB;IACvE,EAAE,OAAM,CAAC;IAET,IAAI;QACF,MAAMC,WAAWC,OAAOC,IAAI,CAACC,iCAAoB,IAAI,CAAC,GAAGC,MAAM,CAAC,CAACC,MAC/DA,IAAIC,UAAU,CAAC;QAEjB,MAAMC,gBAAgB,EAAE;QAExB,KAAK,MAAMF,OAAOL,SAAU;YAC1B,IAAI;gBACF,MAAM,EAAEQ,OAAO,EAAE,GAAGC,QAAQ,CAAC,EAAEJ,IAAI,aAAa,CAAC;gBACjDE,cAAcG,IAAI,CAAC,CAAC,EAAEL,IAAI,CAAC,EAAEG,QAAQ,CAAC;YACxC,EAAE,OAAM,CAAC;QACX;QAEA,IAAID,cAAcI,MAAM,GAAG,GAAG;YAC5BjB,uBAAuBa,cAAcK,IAAI,GAAGC,IAAI,CAAC;QACnD;IACF,EAAE,OAAM,CAAC;IAETvB,UAAUwB,MAAM,CAAC;QACfC,WAAW3B;QACX4B,SAAS;YACPC,aAAAA,oBAAW;YACXxB;YACAC;YACAwB,MAAMvB,QAAQuB,IAAI;YAClBC,UAAUxB,QAAQwB,QAAQ;YAC1BC,aAAazB,QAAQ0B,QAAQ,CAACC,IAAI;YAClCC,IAAI,EAAElC,yBAAAA,MAAOkC,IAAI;YACjBC,uBAAuB,EAAEnC,yBAAAA,MAAOmC,uBAAuB;QACzD;IACF;IACA,oDAAoD;IACpD,MAAMlC,UAAUmC,KAAK;AACvB"}